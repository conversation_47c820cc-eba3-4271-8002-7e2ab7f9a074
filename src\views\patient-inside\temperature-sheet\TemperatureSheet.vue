<template>
  <div class="temperature-sheet-view">
    <el-container>
      <el-aside class="left-aside">
        <div class="left">
          <div class="left-tips">
            <i class="el-icon-warning"></i>
            <span class="el-icon-title">提示:</span>
            <span class="el-icon-content">
              自2019-01-09零点开始，生命体征24h出入量将自动显示到体温单前一天相应的空格内，2019-01-09零点之前的数据照旧规则显示。
            </span>
          </div>
          <div class="left-header">
            <span class="title">体温单选择列表</span>
          </div>
          <div class="temperature-list">
            <el-table :data="stockData" border stripe size="mini" max-height="480px">
              <el-table-column prop="chengFenMC" label="类型"></el-table-column>
              <el-table-column prop="abo" label="血型"></el-table-column>
            </el-table>
          </div>
        </div>
      </el-aside>
    </el-container>
  </div>
</template>

<script>
import { format, differenceInDays } from 'date-fns'
import { mapState } from 'vuex'
export default {
  data() {
    return {
      stockData: [
        { chengFenMC: '2021-09-01', abo: '王小虎' },
        { chengFenMC: '2021-09-01', abo: '王小虎' },
        { chengFenMC: '2021-09-01', abo: '王小虎' },
        { chengFenMC: '2021-09-01', abo: '王小虎' },
        { chengFenMC: '2021-09-01', abo: '王小虎' },
        { chengFenMC: '2021-09-01', abo: '王小虎' },
        { chengFenMC: '2021-09-01', abo: '王小虎' },
        { chengFenMC: '2021-09-01', abo: '王小虎' },
        { chengFenMC: '2021-09-01', abo: '王小虎' },
        { chengFenMC: '2021-09-01', abo: '王小虎' },
        { chengFenMC: '2021-09-01', abo: '王小虎' },
        { chengFenMC: '2021-09-01', abo: '王小虎' },
        { chengFenMC: '2021-09-01', abo: '王小虎' },
        { chengFenMC: '2021-09-01', abo: '王小虎' },
        { chengFenMC: '2021-09-01', abo: '王小虎' },
        { chengFenMC: '2021-09-01', abo: '王小虎' },
        { chengFenMC: '2021-09-01', abo: '王小虎' },
        { chengFenMC: '2021-09-01', abo: '王小虎' },
        { chengFenMC: '2021-09-01', abo: '王小虎' }
      ]
    }
  },
  computed: {
    ...mapState({
      patientDetail: ({ patient }) => patient.patientInit,
      patientInfo: ({ patient }) => patient.initInfo,
      yongHuID: ({ user }) => user.yongHuID
    }),
    bingLiID() {
      return this.$route.params.id
    }
  },
  async mounted() {
    // await this.init()
    console.log(this.patientDetail)
    console.log(this.patientInfo)
  },
  methods: {
    // 页面初始化
    async init() {
      this.listBloodTransSqd()
      this.getBloodBankInventory()
    }
  }
}
</script>

<style lang="scss" scoped>
.temperature-sheet-view {
  background-color: #fff;
  padding: 8px;
  height: 100%;
  .left-aside {
    width: 325px !important;
  }
  .left {
    background-color: #eff3fb;
    border-radius: 4px;
    padding: 12px;
    height: 100%;

    .left-tips {
      display: flex;
      font-size: 14px;
      padding: 6px 10px;
      background: #fff;
      border: 1px solid #a2bae5;
      background-color: #e9f1ff;
      .el-icon-warning {
        font-size: 16px;
        color: #356ac5;
        margin-right: 4px;
      }
      .el-icon-title {
        width: 60px;
      }
      .el-icon-content {
        width: 130%;
        line-height: 20px;
        margin-top: -3px;
      }
    }
    .left-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 10px 0;

      .title {
        position: relative;
        color: #171c28;
        font-size: 14px;
        line-height: 14px;
        font-weight: bold;
        margin-left: 9px;
      }

      .title::before {
        position: absolute;
        left: -9px;
        width: 3px;
        height: 14px;
        content: '';
        background-color: #356ac5;
      }

      ::v-deep .el-button {
        padding: 6px 10px;
        background-color: #3b76ef;
      }
    }

    .temperature-list {
      height: 70%;
      border: 1px solid #dcdfe6;
      background-color: #eaf0f9;
      border-radius: 4px;
      padding: 10px;
    }
  }
}
</style>
