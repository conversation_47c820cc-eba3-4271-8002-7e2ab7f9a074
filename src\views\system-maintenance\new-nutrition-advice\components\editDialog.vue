<template>
  <el-dialog :visible="visible" width="80vw" @open="initFormData()" @close="updateVisible(false)">
    <span slot="title">
      <span class="drawer-title">
        <i class="el-icon-menu"></i>
        肠外营养医嘱单
      </span>
    </span>
    <div>
      <div class="drawer-component">
        <div style="display: flex">
          <div>
            <div>
              <table v-if="drugInfoList">
                <tbody>
                  <template v-for="(item, index) in drugInfoList">
                    <tr v-if="index % 2 === 0" :key="'tr' + index">
                      <td class="info-label">
                        <div class="info-content">
                          {{ item.mingCheng }}
                        </div>
                      </td>
                      <td class="info-value">
                        <div class="info-content">
                          {{ item.jiLiang }}*
                          <el-input
                            v-model="yaoPinXXForm[item.yaoPinID]"
                            @change="inputChange($event, item.yaoPinID)"
                          />
                          {{ item.jiLiangDW }}
                        </div>
                      </td>
                      <template v-if="index + 1 < drugInfoList.length">
                        <td class="info-label">
                          <div class="info-content">
                            {{ drugInfoList[index + 1].mingCheng }}
                          </div>
                        </td>
                        <td class="info-value">
                          <div class="info-content">
                            {{ drugInfoList[index + 1].jiLiang }}*
                            <el-input
                              v-model="yaoPinXXForm[drugInfoList[index + 1].yaoPinID]"
                              @change="inputChange($event, drugInfoList[index + 1].yaoPinID)"
                            />
                            {{ drugInfoList[index + 1].jiLiangDW }}
                          </div>
                        </td>
                      </template>
                    </tr>
                  </template>
                </tbody>
              </table>
            </div>
            <div v-if="formData?.yingYangYZD" class="min-table" style="margin-top: 10px">
              <table>
                <tbody>
                  <tr>
                    <td class="info-label">
                      <div class="info-content">基础能量消耗:</div>
                    </td>
                    <td class="info-value">
                      <div class="info-content">
                        <el-input :value="formData.yingYangYZD.bee" style="width: 70px" />
                        <!-- Kcal/d  -->
                        <el-button type="text" @click="getBEE">(计算)</el-button>
                      </div>
                    </td>
                    <td class="info-label">
                      <div class="info-content">非蛋白能量:</div>
                    </td>
                    <td class="info-value">
                      <div class="info-content">
                        <el-input :value="yingYangYZD.feiDanBNL" style="width: 70px" />
                        Kcal
                      </div>
                    </td>
                    <td class="info-label">
                      <div class="info-content">总氮量:</div>
                    </td>
                    <td class="info-value">
                      <div class="info-content">
                        <el-input :value="yingYangYZD.zongDanLiang" style="width: 80px" />
                        g
                      </div>
                    </td>
                    <td class="info-label">
                      <div class="info-content">总液体量:</div>
                    </td>
                    <td class="info-value">
                      <div class="info-content">
                        <el-input :value="yingYangYZD.zongYeTL" style="width: 80px" />
                        ml
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <div class="info-content">糖:胰岛素:</div>
                    </td>
                    <td class="info-value">
                      <div class="info-content">
                        <el-input :value="yingYangYZD.tangYDS" />
                      </div>
                    </td>
                    <td class="info-label">
                      <div class="info-content">能量:氮:</div>
                    </td>
                    <td class="info-value">
                      <div class="info-content">
                        <el-input :value="yingYangYZD.nengLiangDan" />
                      </div>
                    </td>
                    <td class="info-label">
                      <div class="info-content">糖:脂:</div>
                    </td>
                    <td class="info-value">
                      <div class="info-content">
                        <el-input :value="yingYangYZD.tangZhiFang" />
                      </div>
                    </td>
                    <td class="info-label">
                      <div class="info-content">NRS2002:</div>
                    </td>
                    <td class="info-value">
                      <div class="info-content">
                        <el-input :value="yingYangYZD.nrs" />
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td class="info-label">
                      <div class="info-content">BMI:</div>
                    </td>
                    <td class="info-value">
                      <div class="info-content">
                        <el-input :value="formData.yingYangYZD.bmi" style="width: 70px" />
                        <el-button type="text" @click="getBMI">(计算)</el-button>
                      </div>
                    </td>
                    <td class="info-label">
                      <div class="info-content">渗透压:</div>
                    </td>
                    <td class="info-value">
                      <div class="info-content">
                        <el-input :value="yingYangYZD.shenTouYa" />
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          <div v-if="crrtInfo" class="min-table" style="flex-grow: 1">
            <table style="width: 100%">
              <tbody>
                <tr>
                  <td class="info-label">
                    <div class="info-content">总胆红素:</div>
                  </td>
                  <td class="info-value">
                    <div class="info-content">
                      {{
                        crrtInfo.find((item) => {
                          return item.xiangMuMC === '#总胆红素'
                        })?.jieGuo
                      }}
                    </div>
                  </td>
                  <td class="info-label">
                    <div class="info-content">直接胆红素:</div>
                  </td>
                  <td class="info-value">
                    <div class="info-content">
                      {{
                        crrtInfo.find((item) => {
                          return item.xiangMuMC === '#直接胆红素'
                        })?.jieGuo
                      }}
                    </div>
                  </td>
                </tr>
                <tr>
                  <td class="info-label">
                    <div class="info-content">总蛋白:</div>
                  </td>
                  <td class="info-value">
                    <div class="info-content">
                      {{
                        crrtInfo.find((item) => {
                          return item.xiangMuMC === '#总蛋白'
                        })?.jieGuo
                      }}
                    </div>
                  </td>
                  <td class="info-label">
                    <div class="info-content">白蛋白:</div>
                  </td>
                  <td class="info-value">
                    <div class="info-content">
                      {{
                        crrtInfo.find((item) => {
                          return item.xiangMuMC === '#白蛋白'
                        })?.jieGuo
                      }}
                    </div>
                  </td>
                </tr>

                <tr>
                  <td class="info-label">
                    <div class="info-content">前白蛋白:</div>
                  </td>
                  <td class="info-value">
                    <div class="info-content">
                      {{
                        crrtInfo.find((item) => {
                          return item.xiangMuMC === '#前白蛋白'
                        })?.jieGuo
                      }}
                    </div>
                  </td>
                  <td class="info-label">
                    <div class="info-content">葡萄糖:</div>
                  </td>
                  <td class="info-value">
                    <div class="info-content">
                      {{
                        crrtInfo.find((item) => {
                          return item.xiangMuMC === '#葡萄糖'
                        })?.jieGuo
                      }}
                    </div>
                  </td>
                </tr>

                <tr>
                  <td class="info-label">
                    <div class="info-content">尿素氮:</div>
                  </td>
                  <td class="info-value">
                    <div class="info-content">
                      {{
                        crrtInfo.find((item) => {
                          return item.xiangMuMC === '#尿素氮'
                        })?.jieGuo
                      }}
                    </div>
                  </td>
                  <td class="info-label">
                    <div class="info-content">肝酐:</div>
                  </td>
                  <td class="info-value">
                    <div class="info-content">
                      {{
                        crrtInfo.find((item) => {
                          return item.xiangMuMC === '#肝酐'
                        })?.jieGuo
                      }}
                    </div>
                  </td>
                </tr>

                <tr>
                  <td class="info-label">
                    <div class="info-content">尿酸:</div>
                  </td>
                  <td class="info-value">
                    <div class="info-content">
                      {{
                        crrtInfo.find((item) => {
                          return item.xiangMuMC === '#尿酸'
                        })?.jieGuo
                      }}
                    </div>
                  </td>
                  <td class="info-label">
                    <div class="info-content">甘油三脂:</div>
                  </td>
                  <td class="info-value">
                    <div class="info-content">
                      {{
                        crrtInfo.find((item) => {
                          return item.xiangMuMC === '#甘油三脂'
                        })?.jieGuo
                      }}
                    </div>
                  </td>
                </tr>

                <tr>
                  <td class="info-label">
                    <div class="info-content">丙氨酸氨基转移酶:</div>
                  </td>
                  <td class="info-value">
                    <div class="info-content">
                      {{
                        crrtInfo.find((item) => {
                          return item.xiangMuMC === '#丙氨酸氨基转移酶'
                        })?.jieGuo
                      }}
                    </div>
                  </td>
                  <td class="info-label">
                    <div class="info-content">天冬酸氨基酸氨基转移酶:</div>
                  </td>
                  <td class="info-value">
                    <div class="info-content">
                      {{
                        crrtInfo.find((item) => {
                          return item.xiangMuMC === '#天冬酸氨基酸氨基转移酶'
                        })?.jieGuo
                      }}
                    </div>
                  </td>
                </tr>

                <tr>
                  <td class="info-label">
                    <div class="info-content">碱性膦酸酶:</div>
                  </td>
                  <td class="info-value">
                    <div class="info-content">
                      {{
                        crrtInfo.find((item) => {
                          return item.xiangMuMC === '#碱性膦酸酶'
                        })?.jieGuo
                      }}
                    </div>
                  </td>
                  <td class="info-label">
                    <div class="info-content">血清钾:</div>
                  </td>
                  <td class="info-value">
                    <div class="info-content">
                      {{
                        crrtInfo.find((item) => {
                          return item.xiangMuMC === '#血清钾'
                        })?.jieGuo
                      }}
                    </div>
                  </td>
                </tr>

                <tr>
                  <td class="info-label">
                    <div class="info-content">血清钠:</div>
                  </td>
                  <td class="info-value">
                    <div class="info-content">
                      {{
                        crrtInfo.find((item) => {
                          return item.xiangMuMC === '#血清钠'
                        })?.jieGuo
                      }}
                    </div>
                  </td>
                  <td class="info-label">
                    <div class="info-content">血清氯:</div>
                  </td>
                  <td class="info-value">
                    <div class="info-content">
                      {{
                        crrtInfo.find((item) => {
                          return item.xiangMuMC === '#血清氯'
                        })?.jieGuo
                      }}
                    </div>
                  </td>
                </tr>
                <tr>
                  <td class="info-label">
                    <div class="info-content">血清钙:</div>
                  </td>
                  <td class="info-value">
                    <div class="info-content">
                      {{
                        crrtInfo.find((item) => {
                          return item.xiangMuMC === '#血清钙'
                        })?.jieGuo
                      }}
                    </div>
                  </td>
                  <td class="info-label">
                    <div class="info-content">血清镁:</div>
                  </td>
                  <td class="info-value">
                    <div class="info-content">
                      {{
                        crrtInfo.find((item) => {
                          return item.xiangMuMC === '#血清磷'
                        })?.jieGuo
                      }}
                    </div>
                  </td>
                </tr>

                <tr>
                  <td class="info-label">
                    <div class="info-content">血清镁:</div>
                  </td>
                  <td class="info-value">
                    <div class="info-content">
                      {{
                        crrtInfo.find((item) => {
                          return item.xiangMuMC === '#血清镁'
                        })?.jieGuo
                      }}
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <div v-if="formData?.yingYangYZD" class="min-table" style="margin-top: 10px; width: 100%">
          <table style="width: 100%">
            <tbody>
              <tr>
                <td class="info-label">
                  <div class="info-content">临床诊断:</div>
                </td>
                <td class="info-value" colspan="9">
                  <div class="info-content" style="width: 100%">
                    <el-input v-model="formData.yingYangYZD.linChuangZD" style="width: 100%" />
                  </div>
                </td>
              </tr>

              <tr>
                <td class="info-label">
                  <div class="info-content">开始时间:</div>
                </td>
                <td class="info-value">
                  <div class="info-content" style="width: 100%">
                    <el-date-picker
                      v-model="formData.yingYangYZD.kaiShiSJ"
                      type="datetime"
                      placeholder="选择日期"
                      value-format="yyyy-MM-dd hh:mm:ss"
                      style="width: 100%"
                    />
                  </div>
                </td>
                <td class="info-label">
                  <div class="info-content">持续输液(天):</div>
                </td>
                <td class="info-value">
                  <div class="info-content" style="width: 100%">
                    <el-input-number v-model="formData.yingYangYZD.chiXuTS" style="width: 100%" />
                  </div>
                </td>
                <td class="info-label">
                  <div class="info-content">用药频率:</div>
                </td>
                <td class="info-value">
                  <div class="info-content" style="width: 100%">
                    <el-select v-model="formData.yongYaoPL" placeholder="" style="width: 100%">
                      <el-option
                        v-for="item in yongYaoPL"
                        :key="item.pinLuDM"
                        :label="item.pinLuMC"
                        :value="item.pinLuDM"
                      ></el-option>
                    </el-select>
                  </div>
                </td>
                <td class="info-label">
                  <div class="info-content">用法:</div>
                </td>
                <td class="info-value" style="text-align: center">
                  <div class="info-content">营养静脉滴注</div>
                </td>
                <td class="info-label">
                  <div class="info-content">最少输液时间(小时):</div>
                </td>
                <td class="info-value">
                  <div class="info-content" style="width: 100%">
                    <el-input-number
                      v-model="formData.yingYangYZD.zuiXiaoSYSJ"
                      style="width: 100%"
                    />
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <span class="hint-info">
          <i class="el-icon-warning" />
          <span>
            温馨提示：按六级要求必须走审计系统，营养医嘱【保存】
            【发送至医嘱】后，请至药品医嘱界面提交医嘱。
          </span>
        </span>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button :disabled="!saveLimits" type="primary" @click="uploadFormSave()">保 存</el-button>
      <el-button :disabled="!submitLimits" type="primary" @click="uploadFormSubmit()">
        提 交
      </el-button>
      <el-button @click="updateVisible(false)">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { deepClone } from '@/utils'
import {
  getYaoFangDmInit,
  getDrugNutritionData,
  saveNutritionOrder,
  calculateNutritionDrugData,
  saveNutritionDrugAdvice,
  checkNewNutritionDrugPrivi,
  getBEE,
  getBMI
} from '@/api/nutrition-advice'

import { inpatientInit } from '@/api/inpatient-order'
import { mapState } from 'vuex'

export default {
  name: 'EditDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    dialogType: {
      type: Number,
      default: 0
    },
    //药品详情列表
    drugInfoList: {
      type: Array,
      default: null
    },
    //检查详情
    crrtInfo: {
      type: Array,
      default: null
    },
    nutritionData: {
      type: Object,
      default: null
    },
    nutritionInfo: {
      type: Object,
      default: null
    },
    //保存权限
    saveLimits: {
      type: Boolean,
      default: false
    },
    //人员库id
    renYuanKuID: {
      type: Number,
      default: 0
    },
    //病人体征
    vitalSign: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      notes: '',
      formData: null,
      yaoFangDM: '',
      bingLiID: '2616474', //当前病人
      submitLimits: false, //提交权限
      patientInit: {
        bingRenBH: '33030099000000004200854644',
        jieSuanDM: 'Y09',
        chuShengRQ: '1962-07-26',
        chuangWeiHao: '001',
        lianXiDH: '15957707284',
        lianXiDZ: '龙港市凰浦北路25号',
        xingBie: '1',
        bingRenXM: '潘时章', //姓名
        zhuYuanHao: '2057930',
        zhuYuanID: 2616474,
        shouShuTZD: {
          hunYinZK: '2'
        }
      },
      yingYangYZD: {},
      yaoPinOption: [],
      yaoPinXXForm: {},
      yongYaoPL: [],
      rules: {
        yaoPinID: [{ required: true, message: ' ' }],
        yongYaoPL: [{ required: true, message: ' ' }]
      }
    }
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.user.userInfo
    })
  },
  async mounted() {
    const res = await getYaoFangDmInit({
      yuanQuDM: this.userInfo.yuanQuDM
    })
    console.log('初始化获取营养医嘱单药房代码', res)
    if (res.hasError === 0) {
      this.yaoFangDM = res.data
    }

    const res1 = await inpatientInit({
      bingLiID: this.bingLiID
    })
    console.log('用药频率', res1)
    if (res1.hasError === 0) {
      this.yongYaoPL = res1.data.yongYaoPL
    }
  },
  methods: {
    async getBEE() {
      if (
        this.vitalSign &&
        this.vitalSign.shenGao &&
        this.vitalSign.tiZhong &&
        this.yingYangYZD?.nengLiangDan
      ) {
        const res = await getBEE({
          nengLiang: Number(this.formData?.yingYangYZD?.nengLiangDan.split(':')[0]),
          xingBie: patientInit.xingBie,
          ...this.vitalSign
        })
        console.log('获取基础能量消耗(e_GetBEE)', res)
        if (res.hasError === 0) {
          this.formData.yingYangYZD.bee = res.data
        }
      } else {
        this.$message({
          message: '无法计算',
          type: 'error',
          duration: 700
        })
      }
    },
    async getBMI() {
      if (this.vitalSign && this.vitalSign.shenGao && this.vitalSign.tiZhong) {
        const res = await getBMI({
          ...this.vitalSign
        })
        console.log('计算BMI结果(e_GetBMI)', res)
        if (res.hasError === 0) {
          this.formData.yingYangYZD.bmi = res.data
        }
      } else {
        this.$message({
          message: '无法计算',
          type: 'error',
          duration: 700
        })
      }
    },
    inputChange(value, yaoPinID) {
      console.log(value, Number(value))
      if (!Number(value)) {
        this.yaoPinXXForm[yaoPinID] = ''
      }
      this.computedYingYangYZD()
    },

    async computedYingYangYZD() {
      const drugCalQOS = []
      Object.keys(this.yaoPinXXForm).forEach((key) => {
        if (this.yaoPinXXForm[key]) {
          drugCalQOS.push({
            yaoPinID: key,
            shuLiang: Number(this.yaoPinXXForm[key])
          })
        }
      })
      const res = await calculateNutritionDrugData({
        drugCalQOS
      })

      console.log('计算营养医嘱单药品信息', res)
      if (res.hasError === 0) {
        this.yingYangYZD = res.data
      }
    },
    //保存
    async uploadFormSave() {
      let jieShuSJ = ''
      if (this.formData.yingYangYZD.kaiShiSJ) {
        let futureDate = new Date(this.formData.yingYangYZD.kaiShiSJ)
        futureDate.setDate(futureDate.getDate() + this.formData.yingYangYZD.chiXuTS)
        const year = futureDate.getFullYear()
        const month = futureDate.getMonth() + 1
        const day = futureDate.getDate()
        const hours = futureDate.getHours()
        const minutes = futureDate.getMinutes()
        const seconds = futureDate.getSeconds()
        jieShuSJ =
          year +
          '-' +
          (month < 10 ? '0' + month : month) +
          '-' +
          (day < 10 ? '0' + day : day) +
          ' ' +
          (hours < 10 ? '0' + hours : hours) +
          ':' +
          (minutes < 10 ? '0' + minutes : minutes) +
          ':' +
          (seconds < 10 ? '0' + seconds : seconds)
      }
      const formData = {
        bingLiID: this.bingLiID,

        yaoFangDM: this.yaoFangDM, //药房代码
        yaoPinXX: Object.keys(this.yaoPinXXForm).map((key) => {
          const drugInfo = this.drugInfoList.find((item) => {
            return item.yaoPinID === Number(key)
          })
          return {
            yiZhuDanID: 0,
            yaoPinID: key,
            yiCiYL: this.yaoPinXXForm[key],
            jiLiang: drugInfo.jiLiang,
            jiLiangDW: drugInfo.jiLiangDW
          }
        }), //药品信息
        yingYangYZD: {
          ...this.yingYangYZD,
          ...this.formData.yingYangYZD,
          ...this.vitalSign,
          jieShuSJ,
          bingLiID: this.bingLiID,
          bingRenXM: this.patientInit.bingRenXM,
          bingRenXB: this.patientInit.xingBie,
          zhuYuanHao: this.patientInit.zhuYuanHao,
          yiZhuYSYHID: this.userInfo.yongHuID,
          yiZhuYSYHXM: this.userInfo.yongHuMM,
          zhuangTaiBZ: '1'
        },
        zhuYuanHao: this.patientInit.zhuYuanHao //住院号
      }
      console.log(formData)

      const res = await saveNutritionOrder(formData)

      console.log('保存营养医嘱单', res)
      if (res.hasError === 0) {
        this.$message({
          message: '保存成功',
          type: 'success',
          duration: 700
        })
        this.$emit('upload-success')
      }
    },

    //提交
    async uploadFormSubmit() {
      console.log(this.nutritionData)
      // return
      const res = await saveNutritionDrugAdvice({
        bingLiID: this.bingLiID,
        yaoFangDM: this.yaoFangDM,
        yiZhuDanID: this.nutritionData.yingYangYZD.id, //医嘱单ID
        zhuYuanID: this.patientInit.zhuYuanID //住院ID
      })

      console.log('提交营养医嘱单的药品医嘱', res)
      if (res.hasError === 0) {
        this.$message({
          message: '提交成功',
          type: 'success',
          duration: 700
        })
        this.$emit('upload-success')
      }
    },
    //获取提交权限
    async getSubmitLimits(yiZhuDanID) {
      const res3 = await checkNewNutritionDrugPrivi({
        renYuanKuID: this.renYuanKuID,
        yiZhuDanID: yiZhuDanID
      })

      console.log('判断用户是否有提交权限', res3)
      if (res3.hasError === 0) {
        this.submitLimits = res3.data
      }
    },
    updateVisible(visible) {
      this.$emit('update:visible', visible)
    },
    async initFormData() {
      console.log(this.nutritionData)
      this.formData = deepClone({
        yingYangYZD: this.nutritionData.yingYangYZD
      })
      this.yaoPinXXForm = {}
      this.nutritionData.yaoPinXX.forEach((item) => {
        this.yaoPinXXForm[item.yaoPinID] = item.yiCiYL
      })
      this.getSubmitLimits(this.nutritionData.yingYangYZD.id)
      this.computedYingYangYZD()
    }
  }
}
</script>

<style lang="scss" scoped>
.drawer-title {
  font-size: 16px;
}

.drawer-component {
  font-size: 13px;
  height: 80vh;
  overflow-y: auto;
  padding: 0px 16px;
  table {
    // width: 100%;
  }
  td {
    border: 1px solid #ddd;
    border-collapse: collapse; /* 移除表格内边框间的间隙 */
    height: 35px;
  }
  .info-label {
    text-align: right;
    background-color: #eaf0f9;
    span {
      color: #f35656;
      position: relative;
      top: 3px;
      right: 3px;
    }
  }
  .info-value {
    background-color: #ffffff;
    text-align: right;
    .el-input {
      width: 100px;
    }
  }
  .info-content {
    width: 12vw;
    padding: 5px;
  }
  .min-table {
    .info-value {
      text-align: left;
      .info-content {
        width: 7vw;
      }
    }
    .info-label {
      .info-content {
        width: 5vw;
      }
    }
  }

  .hint-info {
    margin-top: 10px;
    display: flex;
    .el-icon-warning {
      margin: 3px 3px 0 0;
      color: #356ac5;
    }
  }
}
:deep(.el-dialog__footer) {
  border-top: none;
}

:deep(.el-radio) input[aria-hidden='true'] {
  display: none !important;
}
</style>
