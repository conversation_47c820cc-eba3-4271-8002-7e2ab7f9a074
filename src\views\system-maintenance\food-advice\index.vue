<!-- 医院用食品医嘱 -->
<template>
  <div class="food-advice">
    <el-tabs v-model="yiZhuLB" type="border-card" @tab-click="getFoodData">
      <el-tab-pane name="1" label="长期医院用食品医嘱"></el-tab-pane>
      <el-tab-pane name="2" label="临时医院用食品医嘱"></el-tab-pane>
      <el-tab-pane name="3" label="出院外带医院用食品医嘱"></el-tab-pane>
    </el-tabs>
    <div class="inpatient-content">
      <div class="table-title">
        <div>
          <span class="bar" />
          {{ tableTitle }}
        </div>
        <div>
          <el-button
            type="primary"
            @click="
              visible = true
              dialogType = 0
              editData = {}
            "
          >
            新增
          </el-button>
          <!-- <el-button type="primary">刷新</el-button> -->
        </div>
      </div>
      <div class="table-container">
        <el-table
          :data="foodData"
          style="width: 100%"
          stripe
          border
          :row-class-name="tableRowClassName"
          @row-click="handleRowClicked"
        >
          <el-table-column type="index" align="center" width="50"></el-table-column>
          <el-table-column
            v-for="(column, index) in columns"
            :key="index"
            :prop="column.value"
            :label="column.label"
            v-bind="column.props"
          >
            <template slot-scope="scope">
              <template v-if="column.label === '操作'">
                <div style="display: flex; align-items: center; justify-content: center">
                  <el-button
                    type="text"
                    @click="
                      visible = true
                      dialogType = 1
                      editData = scope.row
                    "
                  >
                    编辑
                  </el-button>
                  <el-divider direction="vertical" />
                  <el-button
                    type="text"
                    :disabled="scope.row.zhuangTaiBZ === '0'"
                    @click="
                      hintVisible = true
                      hintType = 1
                      editData = scope.row
                    "
                  >
                    停止
                  </el-button>
                  <el-divider direction="vertical" />
                  <el-button type="text">闭环</el-button>
                  <el-divider direction="vertical" />
                  <el-button
                    type="text"
                    @click="
                      hintVisible = true
                      dialogType = 2
                      hintType = 0
                      editData = scope.row
                    "
                  >
                    删除
                  </el-button>
                </div>
              </template>
              <template v-else-if="column.label === '状态'">
                <el-tag :type="scope.row.zhuangTaiBZ === '1' ? 'success' : 'danger'">
                  {{ scope.row.zhuangTaiBZMC }}
                </el-tag>
              </template>
              <template v-else>
                {{ scope.row[column.value] }}
              </template>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <el-dialog :visible.sync="hintVisible" width="350px">
      <span slot="title">
        <span style="font-size: 16px">
          <i class="el-icon-warning"></i>
          {{ hintType ? '停止' : '删除' }}提示
        </span>
      </span>
      <div class="hint-component">
        确认{{ hintType ? '停止' : '删除' }}
        <a>【{{ editData?.yaoPinMC }}】</a>
        吗?
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="
            hintVisible = false
            hintType ? stopFood(editData.yiZhuID) : saveData(editData)
          "
        >
          确 认
        </el-button>
        <el-button @click="hintVisible = false">取 消</el-button>
      </span>
    </el-dialog>

    <edit-dialog
      :visible.sync="visible"
      :yi-zhu-l-b="yiZhuLB"
      :food-info="foodInfo"
      :dialog-type="dialogType"
      :food-data.sync="editData"
      @upload-success="saveData"
    />
  </div>
</template>

<script>
import {
  getBaseInfoTeYiSPYZ,
  getTeYiSPYZByBlidAndYzlb,
  saveTeYiSPYZ,
  stopTeYiSPYZ
} from '@/api/doctors-advice'

import EditDialog from './components/editDialog.vue'
import { mapState } from 'vuex'

export default {
  name: 'FoodAdvice',
  components: { EditDialog },
  data() {
    return {
      visible: false,
      dialogType: 0, // 0 新增 1 编辑 2 删除
      hintVisible: false,
      hintType: 0, //0 删除 1停止
      editData: {},
      // bingQuID: '3615', //当前病人病区
      bingLiID: '2616474', //当前病人
      // patientInit: {
      //   bingRenBH: '33030099000000004200854644',
      //   jieSuanDM: 'Y09',
      //   chuShengRQ: '1962-07-26',
      //   chuangWeiHao: '001',
      //   lianXiDH: '15957707284',
      //   lianXiDZ: '龙港市凰浦北路25号',
      //   xingBie: '1',
      //   bingRenXM: '潘时章', //姓名
      //   zhuYuanHao: '2057930',
      //   zhuYuanID: 2616474,
      //   shouShuTZD: {
      //     hunYinZK: '2'
      //   }
      // },
      yiZhuLB: '1', //医嘱类别 长期:1 临时:2 出院:3
      foodData: [],
      foodInfo: {},
      clickedRowIndex: null
    }
  },
  computed: {
    // bingLiID() {
    //   return this.$route.params.id
    // },
    ...mapState({
      initInfo: ({ patient }) => patient.initInfo,
      zhiLiaoZuID: ({ patient }) => patient.zhiLiaoZuID,
      bingQuID: ({ patient }) => patient.bingQuID,
      patientInit: ({ patient }) => patient.patientInit
    }),
    tableTitle() {
      switch (this.yiZhuLB) {
        case '1':
          return '长期医院用食品医嘱'
        case '2':
          return '临时医院用食品医嘱'
        case '3':
          return '出院外带医院用食品医嘱'
        default:
          return ''
      }
    },
    columns() {
      return [
        { value: 'shouFeiSJ', label: '收费时间', props: { width: '170' } },
        { value: 'zhuangTaiBZ', label: '状态', props: { align: 'center' } },
        { value: 'yaoPinMC', label: '药品名称', props: { width: '200' } },
        { value: 'guiGe', label: '规格' },
        { value: 'baoZhuangLiang', label: '包装量' },
        { value: 'yiCiYL', label: '一次用量' },
        { value: 'danWei', label: '单位' },
        { value: 'yongYaoFFMC', label: '用药方法' },
        { value: 'yongYaoPLMC', label: '频率' },
        { value: 'geiYaoSJMC', label: '给药时间' },
        { value: 'yongFaBCSM', label: '用法补充说明', props: { width: '130' } },
        ...(this.yiZhuLB !== '1'
          ? [{ value: 'shuLiang', label: '数量', props: { width: '170' } }]
          : []),
        { value: 'danJia', label: '单价' },
        ...(this.yiZhuLB !== '3'
          ? [{ value: 'kaiShiSJ', label: '计划开始时间', props: { width: '170' } }]
          : []),
        ...(this.yiZhuLB === '1'
          ? [{ value: 'jieShuSJ', label: '计划结束时间', props: { width: '170' } }]
          : [{ value: 'yuJiKYTS', label: '预计可用天数', props: { width: '170' } }]),
        { value: 'yiShengXM', label: '医师姓名' },
        { value: 'yiZhuSJ', label: '医嘱时间', props: { width: '170' } },
        { label: '操作', props: { fixed: 'right', width: '230', align: 'center' } }
      ]
    }
  },
  async mounted() {
    const res = await getBaseInfoTeYiSPYZ({
      bingLiID: this.bingLiID,
      yiZhuLB: this.yiZhuLB
    })

    console.log('特医食品医嘱初始化信息', res)
    if (res.hasError === 0) {
      this.foodInfo = res.data
    }

    this.getFoodData()
  },
  methods: {
    //初始化列表
    async getFoodData() {
      const res = await getTeYiSPYZByBlidAndYzlb({
        bingLiID: this.bingLiID,
        yiZhuLB: this.yiZhuLB
      })

      console.log('特医食品医嘱', res)
      if (res.hasError === 0) {
        this.foodData = res.data
      }
    },
    //保存
    async saveData(data) {
      console.log(data)
      const params = {
        bingLiID: this.bingLiID,
        zhuYuanID: this.patientInit.zhuYuanID, //住院id
        yiZhuLB: this.yiZhuLB
      }

      params[['adds', 'updates', 'deletes'][this.dialogType]] = [
        {
          bingLiID: this.bingLiID,
          zhuYuanID: this.patientInit.zhuYuanID, //住院id
          bingRenXM: this.patientInit.bingRenXM, //病人姓名
          bingQuID: this.bingQuID, //病区id
          zhuanKeID: this.initInfo.zhuanKeID, //专科id
          zhiLiaoZuID: this.zhiLiaoZuID, //治疗组id
          yiZhuLB: this.yiZhuLB, //医嘱类别 长期:1 临时:2 出院:3
          ...data
        }
      ]
      const res = await saveTeYiSPYZ(params)

      console.log('保存食品医嘱', res)
      if (res.hasError === 0) {
        this.$message({
          message: '保存成功',
          type: 'success',
          duration: 700
        })
        this.getFoodData()
      }
    },
    //停止
    async stopFood(yiZhuID) {
      const res = await stopTeYiSPYZ({
        yiZhuIDs: [yiZhuID],
        bingLiID: this.bingLiID,
        yiZhuLB: this.yiZhuLB //医嘱类别 长期:1 临时:2 出院:3
      })

      console.log('停止食品医嘱', res)
      if (res.hasError === 0) {
        this.$message({
          message: '保存成功',
          type: 'success',
          duration: 700
        })
        this.getFoodData()
      }
    },
    // 是否可选
    isRowSelectable(row) {
      return false
    },
    // 行点击高亮
    handleRowClicked(row) {
      return
      const index = this.foodData.indexOf(row)
      if (this.clickedRowIndex === index) {
        this.clickedRowIndex = null
      } else {
        this.clickedRowIndex = this.foodData.indexOf(row)
      }
    },
    // 动态绑定行类名
    tableRowClassName({ row, rowIndex }) {
      let className = ''
      if (rowIndex === this.clickedRowIndex) {
        className += 'selected-row '
      }
      switch (row.yanSe) {
        case 'blue':
          className += 'blue-row '
          break
        case 'red':
          className += 'red-row '
          break
        case 'black':
        default:
          className += ''
      }
      return className.trim()
    }
  }
}
</script>

<style scoped lang="scss">
.food-advice {
  background: #eff3fb;
  padding: 8px;
  height: 100%;

  ::v-deep .el-tabs--border-card {
    box-shadow: none;
    background: #eff3fb;
    .el-tabs__header .el-tabs__nav {
      .el-tabs__item {
        height: 32px;
        line-height: 32px;
        background: #ffffff;
        border: 1px solid #dcdfe6;
        &.is-active {
          background: #eff3fb;
          border-bottom-color: transparent;
        }
      }
    }
    .el-tabs__content {
      padding: 0px;
      .el-tab-pane {
        display: flex;
        flex-direction: column;
      }
    }
  }

  .inpatient-content {
    padding-top: 10px;
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;

    .table-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-weight: 600;
      margin-bottom: 10px;
      .bar {
        border-left: 3px solid #356ac5;
        padding-left: 5px;
      }
      .sub-title {
        font-weight: 400;
      }
    }
    .table-container {
      flex: 1;
      position: relative;
      ::v-deep .el-drawer__wrapper {
        position: absolute;
        pointer-events: none;
        .el-drawer__header {
          padding: 4px 8px;
          .drawer-header {
            display: flex;
            align-items: center;
            position: relative;
            .title {
              margin-bottom: 0;
              margin-right: 8px;
            }
            .full-button {
              position: absolute;
              top: -4px;
              left: 50%;
              transform: translateX(-50%);
              width: 68px;
              height: 16px;
              text-align: center;
              line-height: 16px;
              font-size: 16px;
              background: #ecf1f9;
              border-radius: 0 0 6px 6px;
              cursor: pointer;
            }
          }
        }
        .el-drawer__container {
          pointer-events: none;
        }

        .el-drawer {
          pointer-events: auto;
        }
      }
    }
    .footer-action {
      margin-top: 8px;
      height: 58px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      background: #eaf0f9;
      display: flex;
      align-items: center;
      padding-left: 10px;
      .el-checkbox {
        margin-left: var(--common-margin);
        margin-right: 0;
      }
    }
    ::v-deep .el-table {
      .el-checkbox__input.is-disabled {
        display: none;
      }
      .el-checkbox__inner {
        width: 16px;
        height: 16px;
        &:after {
          height: 8px;
          left: 5px;
        }
      }
      .selected-row td.el-table__cell {
        background-color: #6787cc;
        color: #fff;
      }
      .blue-row td.el-table__cell {
        color: #356ac5;
      }
      .red-row td.el-table__cell {
        color: #f56c6c;
      }
      .hover-row:not(.selected-row) td.el-table__cell {
        background: #eff3fb;
      }
    }
  }

  :deep(.el-dialog__footer) {
    border-top: none;
  }
  .el-icon-warning {
    font-size: 20px;
    color: #ed6a0c;
  }
  .hint-component {
    padding-left: 30px;
    color: #96999e;
    a {
      color: #356ac5;
    }
  }
}
</style>
