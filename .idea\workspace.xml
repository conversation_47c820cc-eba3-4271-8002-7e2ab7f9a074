<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="c083a2b4-f987-46de-9ed7-8700a9d65573" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="2tsN6VP0mo6wbhbtTYPYNUliqK7" />
  <component name="ProjectViewState">
    <option name="autoscrollFromSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;com.codeium.enabled&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/项目/icube2&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-deb605915726-JavaScript-WS-243.22562.222" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="c083a2b4-f987-46de-9ed7-8700a9d65573" name="更改" comment="" />
      <created>1741136647832</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1741136647832</updated>
      <workItem from="1741136650356" duration="17000" />
      <workItem from="1741938243258" duration="2435000" />
      <workItem from="1742182272598" duration="6469000" />
      <workItem from="1742258414259" duration="7503000" />
      <workItem from="1742535842532" duration="8616000" />
      <workItem from="1742777139737" duration="13974000" />
      <workItem from="1742800721158" duration="4164000" />
      <workItem from="1742863303923" duration="364000" />
      <workItem from="1742863707123" duration="52425000" />
      <workItem from="1743123722164" duration="4942000" />
      <workItem from="1743131660366" duration="1040000" />
      <workItem from="1743140605191" duration="597000" />
      <workItem from="1743383745375" duration="61383000" />
      <workItem from="1743669393447" duration="984000" />
      <workItem from="1744074288438" duration="88000" />
      <workItem from="1744074422799" duration="34035000" />
      <workItem from="1744247975510" duration="1000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>