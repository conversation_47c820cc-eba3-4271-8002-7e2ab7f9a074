<template>
  <div class="surgical-notice-view">
    <el-container>
      <el-aside class="left-aside">
        <div class="left">
          <div class="left-header">
            <span class="title">手术通知单列表</span>
            <el-button type="primary" @click="handleAdd">新增</el-button>
          </div>
          <div class="surgical-list">
            <table v-for="item in shouShuTZDList" :key="item.bingLiID" @click="handleTZDInit(item)">
              <thead>
                <th v-if="item.shouShuXM[0].shouShuBW" class="table-header" colspan="3">
                  {{ item.shouShuXM[0].shouShuMC }}({{ item.shouShuXM[0].shouShuBW }})
                </th>
                <th v-else class="table-header" colspan="3">
                  {{ item.shouShuXM[0].shouShuMC }}
                </th>
              </thead>
              <tbody>
                <tr>
                  <td class="table-title">开单时间:</td>
                  <td class="text-center" colspan="2">{{ item.kaiDanSJ }}</td>
                </tr>
                <tr>
                  <td class="table-title">主刀医师:</td>
                  <td class="text-center">{{ item.zhuDaoYSXM }}</td>
                  <td class="text-center" style="color: #356ac5">{{ item.shenPiYJMC }}</td>
                </tr>
                <tr>
                  <td class="table-title">状态:</td>
                  <td class="text-center" style="border-right: none">
                    <el-tag hit color="#E2E8F4">{{ zhuangTaiMC(item.zhuangTaiBZ) }}</el-tag>
                  </td>
                  <td class="text-center" style="border-left: none">
                    <el-tag hit color="#E2E8F4">{{ item.shiFouJZMC }}</el-tag>
                  </td>
                </tr>
                <tr class="last-tr">
                  <td class="table-title last-td">房间:</td>
                  <td class="last-td text-center" style="color: #f35656" colspan="2">
                    {{ item.shouShuJianMC }}-{{ item.taiXu }}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </el-aside>
      <el-main>
        <div class="right">
          <div class="right-header">
            <div class="title">手术通知单</div>
            <div class="button-group">
              <el-button type="primary" :disabled="saveDisabled" @click="handleSave">
                保存
              </el-button>
              <el-button type="primary">删除</el-button>
              <el-popover
                v-model="visible"
                popper-class="right-header-poperover"
                placement="bottom"
                width="240"
              >
                <div class="popover">
                  <div class="popover-item">打印</div>
                  <div class="popover-item">一次性手术器械路域相关信息查询</div>
                  <div class="popover-item">手术闭环</div>
                  <div class="popover-item">申请停台</div>
                  <div class="popover-item">申请手术耗材</div>
                </div>

                <el-button slot="reference"><i class="el-icon-more"></i></el-button>
              </el-popover>
            </div>
          </div>
          <div class="right-content">
            <el-container>
              <el-main>
                <table class="right-table">
                  <tr>
                    <td class="right-table-title">姓名:</td>
                    <td class="right-table-content">{{ initTZD.bingRenXM }}</td>
                    <td class="right-table-title">性别:</td>
                    <td class="right-table-content">
                      {{ initTZD.bingRenXB == '1' ? '男' : '女' }}
                    </td>
                    <td class="right-table-title">出生日期:</td>
                    <td class="right-table-content">
                      {{ formatDate(initTZD.chuShengRQ, 'yyyy-MM-dd') }}
                    </td>
                  </tr>
                  <tr>
                    <td class="right-table-title">职业:</td>
                    <td class="right-table-content">{{ initTZD.zhiYeMC }}</td>
                    <td class="right-table-title">病区:</td>
                    <td class="right-table-content">
                      {{ initTZD.bingQuMC }}
                    </td>
                    <td class="right-table-title">床位:</td>
                    <td class="right-table-content">{{ initTZD.chuangWeiHao }}</td>
                  </tr>
                  <tr>
                    <td class="right-table-title">病案号:</td>
                    <td class="right-table-content">{{ initTZD.bingAnHao }}</td>
                    <td class="right-table-title">入院日期:</td>
                    <td class="right-table-content">
                      {{ formatDate(initTZD.ruYuanRQ, 'yyyy-MM-dd') }}
                    </td>
                    <td class="right-table-title">临床诊断:</td>
                    <td class="right-table-content">{{ initTZD.linChuangZD }}</td>
                  </tr>
                  <tr>
                    <td class="right-table-title">主刀医生:</td>
                    <td class="right-table-content" @dblclick="selectYS('zhuDaoYSXM')">
                      <el-input v-model="shouShuTZD.zhuDaoYSXM" class="select-input"></el-input>
                    </td>
                    <td class="right-table-title">台上指导:</td>
                    <td class="right-table-content" @dblclick="selectYS('taiShangZDXM')">
                      <el-input v-model="shouShuTZD.taiShangZDXM" class="select-input"></el-input>
                    </td>
                    <td class="right-table-title">参观者:</td>
                    <td class="right-table-content">
                      <el-input v-model="shouShuTZD.canGuanZhe"></el-input>
                    </td>
                  </tr>
                  <tr>
                    <td class="right-table-title">第一助手:</td>
                    <td class="right-table-content" @dblclick="selectYS('diYiZSXM')">
                      <el-input v-model="shouShuTZD.diYiZSXM" class="select-input"></el-input>
                    </td>
                    <td class="right-table-title">第二助手:</td>
                    <td class="right-table-content" @dblclick="selectYS('diErZSXM')">
                      <el-input v-model="shouShuTZD.diErZSXM" class="select-input"></el-input>
                    </td>
                    <td class="right-table-title">开单者:</td>
                    <td class="right-table-content"></td>
                  </tr>
                  <tr>
                    <td class="right-table-title">第三助手:</td>
                    <td class="right-table-content" @dblclick="selectYS('diSanZSXM')">
                      <el-input v-model="shouShuTZD.diSanZSXM" class="select-input"></el-input>
                    </td>
                    <td class="right-table-title">第四助手:</td>
                    <td class="right-table-content" @dblclick="selectYS('diSiZSXM')">
                      <el-input v-model="shouShuTZD.diSiZSXM" class="select-input"></el-input>
                    </td>
                    <td
                      colspan="2"
                      style="
                        text-align: center;
                        text-decoration: underline;
                        color: #356ac5;
                        cursor: pointer;
                        background-color: #fff;
                      "
                    >
                      一类手术
                    </td>
                  </tr>
                  <tr
                    v-for="row in shuZhongHUiZhenRowNum(shuZhongHuiZhenList.length + 1)"
                    :key="row"
                  >
                    <template
                      v-for="col in shuZhongHUiZhenColNum(row, shuZhongHuiZhenList.length + 1)"
                    >
                      <td
                        v-if="row == 1 && col == 1"
                        :key="col"
                        style="text-align: center"
                        colspan="2"
                      >
                        <el-button style="color: #356ac5" @click="handleAddShuZhongHZ">
                          新增术中会诊
                        </el-button>
                      </td>
                      <template v-else>
                        <td :key="col" class="right-table-title">门诊医生:</td>
                        <td :key="col" class="right-table-content">
                          <el-input
                            v-model="shuZhongHuiZhenList[(row - 1) * 3 + col - 2].huiZhenYSXM"
                            class="select-input"
                          ></el-input>
                        </td>
                      </template>
                    </template>
                  </tr>
                  <tr>
                    <td class="right-table-title">拟施手术:</td>
                    <td class="right-table-content" colspan="5" @dblclick="selectShouShu">
                      <div class="niShiShouShuList">
                        <span @dblclick.stop>
                          <el-tag
                            v-for="tag in niShiShouShuList"
                            :key="tag.shouShuDM"
                            closable
                            @close="handleNiShiSSTagClose"
                          >
                            {{ tag.shouShuMC }}
                          </el-tag>
                        </span>
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td class="right-table-title">手术室:</td>
                    <td class="right-table-content">
                      <el-select v-model="initTZD.shouShuShiDM" placeholder="请选择">
                        <el-option
                          v-for="item in initTZD.shouShuShiList"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </td>
                    <td class="right-table-title">专科小组:</td>
                    <td class="right-table-content">
                      <el-select v-model="shouShuTZD.youXianJB" placeholder="请选择">
                        <el-option
                          v-for="item in initTZD.youXianJBList"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </td>
                    <td
                      class="right-table-title"
                      style="
                        text-align: left;
                        display: flex;
                        width: 100%;
                        flex-direction: row-reverse;
                      "
                    >
                      <span>:</span>
                      <span>预计手术时间(分钟)</span>
                    </td>
                    <td class="right-table-content">
                      <el-select v-model="shouShuTZD.yuJiSSSC" placeholder="请选择">
                        <el-option
                          v-for="item in initTZD.yuJiSSSCList"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </td>
                    <!-- <td class="right-table-content">{{ tongZhiDanTableData.yuJiSSSC }}</td> -->
                  </tr>
                  <tr>
                    <td class="right-table-title">医疗小组:</td>
                    <td class="right-table-content">
                      <el-select v-model="shouShuTZD.zhuanKeXZ" placeholder="请选择">
                        <el-option
                          v-for="item in initTZD.zhuanKeXZList"
                          :key="item.id.toString()"
                          :label="item.mingCheng"
                          :value="item.id.toString()"
                        ></el-option>
                      </el-select>
                    </td>
                    <td class="right-table-title">小组台序:</td>
                    <td class="right-table-content">
                      <el-select v-model="shouShuTZD.xiaoZuTX" placeholder="请选择">
                        <el-option
                          v-for="item in xiaoZuTaiXuList"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </td>
                    <td
                      class="right-table-title"
                      style="
                        text-align: left;
                        display: flex;
                        width: 100%;
                        flex-direction: row-reverse;
                        align-items: center;
                      "
                    >
                      <span>:</span>
                      <span>拟施手术日期</span>
                    </td>
                    <td class="right-table-content">
                      {{ formatDate(initTZD.niShouShuSJ, 'yyyy-MM-dd') }}
                    </td>
                  </tr>
                  <tr>
                    <td class="right-table-title">隔离种类:</td>
                    <td class="right-table-content">
                      <el-select v-model="shouShuTZD.geLiDM" placeholder="请选择">
                        <el-option
                          v-for="item in initTZD.geLiZL"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </td>
                    <!-- 缺失字段 -->
                    <td
                      class="right-table-title"
                      style="
                        text-align: left;
                        display: flex;
                        width: 100%;
                        flex-direction: row-reverse;
                      "
                    >
                      <span>:</span>
                      <span>传染病阳性指标</span>
                    </td>
                    <td colspan="3" class="right-table-content"></td>
                  </tr>
                  <tr>
                    <!-- 缺失字段 -->
                    <td class="right-table-title">预约冰冻切片:</td>
                    <td class="right-table-content">
                      <el-radio v-model="initTZD.yuYueBDQP" label="0">否</el-radio>
                      <el-radio v-model="initTZD.yuYueBDQP" label="1">是</el-radio>
                    </td>
                    <td class="right-table-title">麻醉会诊:</td>
                    <td class="right-table-content">
                      <el-checkbox v-model="maZuiHZMapper"></el-checkbox>
                    </td>
                    <td class="right-table-title">急诊:</td>
                    <td class="right-table-content">
                      <el-checkbox v-model="shouShuTZD.shiFouJZ"></el-checkbox>
                    </td>
                  </tr>

                  <tr>
                    <!-- 字段缺失,没找到字段 -->
                    <td class="right-table-title">日间手术:</td>
                    <td class="right-table-content">
                      <el-checkbox v-model="riJianSS"></el-checkbox>
                    </td>
                    <!-- 字段缺失,没找到字段 -->
                    <td class="right-table-title">需要技师:</td>
                    <td class="right-table-content">
                      <el-checkbox v-model="xuYaoJS"></el-checkbox>
                    </td>
                    <td colspan="2" class="right-table-title"></td>
                  </tr>
                  <tr>
                    <td colspan="2" class="right-table-title" style="text-align: center">
                      <el-button style="color: #356ac5">术前手术室材料申请</el-button>
                    </td>
                    <td class="right-table-title">特殊手术体位:</td>
                    <td class="right-table-content">
                      <el-select v-model="initTZD.teShuSSTW" placeholder="请选择">
                        <el-option
                          v-for="item in initTZD.teShuSSTWList"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </td>
                    <td class="right-table-title">开单时间:</td>
                    <td class="right-table-content">{{ shouShuTZD.kaiDanSJ }}</td>
                  </tr>
                  <tr>
                    <!-- 字段缺失，没找到字段 -->
                    <td class="right-table-title">其它要求:</td>
                    <td class="right-table-content">
                      <el-checkbox v-model="qiYaZL">气压治疗</el-checkbox>
                      <el-checkbox v-model="zhongDaSS">重大手术</el-checkbox>
                    </td>
                    <td class="right-table-title">新技术新项目:</td>
                    <td colspan="3" class="right-table-content" @dblclick="selectXinJiShuXXM">
                      <el-select
                        v-model="shouShuTZD.xinJiShuXXM"
                        style="width: 100%"
                        placeholder="请选择"
                      >
                        <el-option
                          v-for="item in xinJiShuXXMList"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </td>
                  </tr>
                  <tr>
                    <td class="right-table-title">术前讨论:</td>
                    <td colspan="5" class="right-table-content">
                      <div
                        style="display: flex; align-items: center; justify-content: space-between"
                      >
                        <span>未关联</span>
                        <div>
                          <el-button>选择</el-button>
                          <el-button>取消关联</el-button>
                        </div>
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td class="right-table-title">知情同意书:</td>
                    <td colspan="5" class="right-table-content">
                      <div
                        style="display: flex; align-items: center; justify-content: space-between"
                      >
                        <span>未关联</span>
                        <div>
                          <el-button>选择</el-button>
                          <el-button>取消关联</el-button>
                        </div>
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td class="right-table-title" style="vertical-align: top">
                      <div>特殊要求:</div>
                      <div style="color: #155bd4; text-decoration: underline">文字模板</div>
                    </td>
                    <td class="right-table-content" colspan="5">
                      <el-input
                        v-model="shouShuTZD.kaiDanBZ"
                        type="textarea"
                        :autosize="{ minRows: 6, maxRows: 15 }"
                        placeholder="请输入内容"
                        maxlength="1000"
                        show-word-limit
                        :clearable="true"
                        :rows="10"
                      ></el-input>
                    </td>
                  </tr>
                  <tr>
                    <td class="right-table-title">审批医师:</td>
                    <td class="right-table-content">{{ shouShuTZD.shenPiYSXM }}</td>
                    <td class="right-table-title">审批意见:</td>
                    <td class="right-table-content">{{ shouShuTZD.shenPiYJ }}</td>
                    <td class="right-table-title">审批时间:</td>
                    <td class="right-table-content">
                      {{ formatDate(shouShuTZD.shenPiSJ, 'yyyy-MM-dd') }}
                    </td>
                  </tr>
                  <tr>
                    <td class="right-table-title">手术房间:</td>
                    <td class="right-table-content">{{ shouShuTZD.shouShuJianMC }}</td>
                    <td class="right-table-title">手术时间:</td>
                    <td class="right-table-content">
                      {{ formatDate(shouShuTZD.niShouShuSJ, 'yyyy-MM-dd') }}
                    </td>
                    <td class="right-table-title">实施手术:</td>
                    <td class="right-table-content">
                      {{ shiShiShouShuMapper(shouShuTZD.shiShiSS) }}
                    </td>
                  </tr>
                  <tr>
                    <td class="right-table-title">麻醉医师1:</td>
                    <td class="right-table-content">{{ shouShuTZD.maZuiYS1XM }}</td>
                    <td class="right-table-title">麻醉医师2:</td>
                    <td class="right-table-content">{{ shouShuTZD.maZuiYS2XM }}</td>
                    <td class="right-table-title">麻醉医师3:</td>
                    <td class="right-table-content">{{ shouShuTZD.maZuiYS3XM }}</td>
                  </tr>
                  <tr>
                    <td class="right-table-title">器械护士1:</td>
                    <td class="right-table-content">{{ shouShuTZD.qiXieHS1XM }}</td>
                    <td class="right-table-title">巡回护士1:</td>
                    <td class="right-table-content">{{ shouShuTZD.xunHuiHS1XM }}</td>
                    <td class="right-table-title">感染处置:</td>
                    <td class="right-table-content">{{ shouShuTZD.ganRanCZ1XM }}</td>
                  </tr>
                  <tr>
                    <td class="right-table-title">器械护士2:</td>
                    <td class="right-table-content">{{ shouShuTZD.qiXieHS2XM }}</td>
                    <td class="right-table-title">巡回护士2:</td>
                    <td class="right-table-content">{{ shouShuTZD.xunHuiHS2XM }}</td>
                    <td colspan="2" class="right-table-title"></td>
                  </tr>
                  <tr>
                    <td colspan="6" class="right-table-title">
                      <div class="title">说明</div>
                    </td>
                  </tr>
                  <tr>
                    <td class="right-table-footer-tip" colspan="6">
                      <div style="background-color: #ffffff; padding: 8px">
                        <div>
                          1.医师在开通知单时请仔细核对
                          <span style="color: #ed6a0c">自己和主刀医师当前的专科。</span>
                        </div>
                        <div>
                          2.通知单
                          <span style="color: #ed6a0c">审批时的专科</span>
                          由
                          <span style="color: #ed6a0c">主刀医师</span>
                          的当前专科决定。比如：如果主刀医师是A专科，则也需要A专科医师审批。
                        </div>
                        <div>
                          3.平诊手术通知单接收时间为
                          <span style="color: #ed6a0c">05：00</span>
                          至
                          <span style="color: #ed6a0c">11：00</span>
                          ，如有特殊情况请与手术室联系。
                        </div>
                        <div>
                          4.日间手术开单时间在
                          <span style="color: #ed6a0c">15：00</span>
                          后，需电话联系手术室。
                        </div>
                        <div>
                          ——》
                          <span style="color: #155bd4; text-decoration: underline">
                            术前手术室材料使用教程
                          </span>
                          《—— ——》
                          <span style="color: #155bd4; text-decoration: underline">
                            医排护排手术修改
                          </span>
                          《——
                        </div>
                      </div>
                    </td>
                  </tr>
                </table>
              </el-main>
              <el-aside class="right-aside">
                <div class="right-aside-header">
                  <el-button size="mini">导入化验结果</el-button>
                </div>
                <table class="right-aside-table">
                  <thead>
                    <tr>
                      <td>化验名称</td>
                      <td>结果</td>
                      <td>化验时间</td>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>其它</td>
                      <td></td>
                      <td></td>
                    </tr>
                  </tbody>
                </table>
              </el-aside>
            </el-container>
          </div>
        </div>
      </el-main>
    </el-container>
    <el-dialog width="30%" class="Dialog" :visible.sync="zhuDaoYSDialog">
      <div slot="title" class="title">人员选择</div>
      <div class="flex">
        <el-input
          v-model="renYuanXM"
          class="renYuanXMInput"
          placeholder="请输入人员终身码或拼音/五笔码"
          @change="queryZhuDaoYS"
        ></el-input>
        <el-checkbox v-model="benZhuanKe">本专科</el-checkbox>
      </div>
      <table class="renYuanList">
        <thead>
          <tr>
            <td>姓名</td>
            <td>终身码</td>
            <td>所在专科</td>
            <td>人员类别</td>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="item in currentPageZhuDaoYSList"
            :key="item.yongHuID"
            @dblclick="handleSelectRY(item)"
          >
            <td>{{ item.xingMing }}</td>
            <td>{{ item.zhongShenDM }}</td>
            <td>{{ item.xianZhuanKeMC }}</td>
            <td>{{ item.renYuanLBMC }}</td>
          </tr>
        </tbody>
      </table>
      <el-pagination
        class="pagination"
        small
        background
        :current-page.sync="currentPage"
        :page-size="pageSize"
        layout="total, prev, pager, next"
        :total="zhuDaoYSListFilter.length"
      ></el-pagination>
      <div class="flex bottom-btn">
        <el-button size="mini">关闭</el-button>
        <el-button size="mini" type="primary">确认</el-button>
      </div>
    </el-dialog>
    <el-dialog width="50%" class="Dialog" :visible.sync="niShiShouShuDialog">
      <div slot="title" class="title">手术选择窗口</div>
      <div class="flex shouShuXZHeader shouShuHeader">
        <span class="title">手术列表</span>
        <div class="flex" style="flex-grow: 0.1">
          <span style="width: 63%; font-weight: 600">输入拼音五笔(回撤结束):</span>
          <el-input v-model="shouShuQueryMC" @change="queryZhuDaoYS"></el-input>
        </div>
      </div>
      <div class="shouShuTable shouShuXZList">
        <el-table
          ref="shouShuXZList"
          :data="currentPageShouShuXZList"
          tooltip-effect="dark"
          style="width: 100%"
          :row-key="getRowKeys"
          @selection-change="handleSelectChange"
        >
          <el-table-column type="selection" width="55" :reserve-selection="true"></el-table-column>
          <el-table-column prop="shouShuDM" label="手术代码" width="120"></el-table-column>
          <el-table-column prop="shouShuMC" label="手术名称" width="120"></el-table-column>

          <el-table-column prop="shouShuJB" label="手术级别"></el-table-column>
          <el-table-column prop="ICD" label="ICD9"></el-table-column>
        </el-table>
        <el-pagination
          class="pagination"
          small
          background
          :current-page.sync="shouShuXZCurrentPage"
          :page-size="shouShuXZPageSize"
          layout="total, prev, pager, next"
          :total="shouShuXZList.length"
        ></el-pagination>
      </div>
      <div class="yiXuanZeSSHeader shouShuHeader">
        <span class="title">已选择的手术</span>
      </div>
      <div class="shouShuTable yiXuanZeShouShuList">
        <el-table
          ref="shouShuSelectedList"
          :data="shouShuXZSelectionList"
          tooltip-effect="dark"
          :row-key="getRowKeys"
          style="width: 100%; height: 100%; overflow-y: scroll"
          @cell-click="handleShouShuBWClick"
        >
          <el-table-column type="selection" width="55" :reserve-selection="true"></el-table-column>
          <el-table-column prop="shouShuDM" label="手术代码" width="120"></el-table-column>
          <el-table-column prop="shouShuMC" label="手术名称" width="120"></el-table-column>
          <el-table-column prop="shouShuBW" label="手术部位" width="120"></el-table-column>
          <el-table-column prop="shouShuJB" label="手术级别" width="120"></el-table-column>
          <el-table-column prop="ICD" label="ICD9"></el-table-column>
        </el-table>
      </div>
      <div style="margin-top: 20px">
        <i class="el-icon-info" style="color: #356ac5"></i>
        <span style="margin-left: 1px">
          手术选择后点完成按钮返回主界面，如需调整手术准入，请联系医务科。
        </span>
      </div>
      <div style="float: right; padding-bottom: 16px">
        <el-button type="primary" @click="handleNiShiShouShuSubmit">确定</el-button>
        <el-button>关闭</el-button>
      </div>
    </el-dialog>
    <el-dialog width="20%" class="Dialog shouShuBWDialog" :visible.sync="shouShuBWDialog">
      <div slot="title" class="title">选择手术部位</div>
      <el-table :data="shouShuBWList">
        <el-table-column label="选择手术部位">
          <template slot-scope="scope">
            <el-radio v-model="shouShuBW" :label="scope.row.buWeiMC">
              {{ scope.row.buWeiMC }}
            </el-radio>
          </template>
        </el-table-column>
      </el-table>
      <div style="float: right; padding: 20px 0">
        <el-button type="primary" size="mini" @click="handleSelectShouShuBW">确定</el-button>
        <el-button type="primary" size="mini">重置</el-button>
        <el-button size="mini">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  inpatientInit,
  initShouShuTZDList,
  initShouShuTZD,
  getListByZhuanKeID,
  updateShouShuTZD,
  addShouShuTZD,
  getShouShuRyk
} from '@/api/inpatient-order'
import { xiaoZuTaiXuList } from './init/surgicalNoticeInit'
import { format } from 'date-fns'
import { mapState } from 'vuex'
export default {
  data() {
    return {
      shuZhongHuiZhenList: [],
      saveDisabled: false,
      xinJiShuXXMList: [],
      shouShuTZD: {},
      inpatientInit: {},
      initTZD: {},
      maZuiHZ: false,
      canGuanZhe: '',
      riJianSS: false,
      xuYaoJS: false,
      teShuYQ: '',
      zhuYuanID: null, //住院ID
      visible: false, //头部右侧三个点按钮是否显示
      shouShuTZDList: [], //通知单列表
      zhuDaoYSDialog: false, //主刀医生选择窗口
      niShiShouShuDialog: false, //拟施手术窗口
      shouShuBWDialog: false, //手术部位窗口
      shouShuBWSelection: {}, //手术部位选择
      shouShuBW: '左侧',
      zhuDaoYSList: [], //主刀医生列表
      currentPage: 1,
      pageSize: 13, //每页条数
      shouShuBWList: [
        {
          buWeiMC: '左侧'
        },
        {
          buWeiMC: '右侧'
        },
        {
          buWeiMC: '双侧'
        },
        {
          buWeiMC: '上段'
        },
        {
          buWeiMC: '中段'
        },
        {
          buWeiMC: '下段'
        }
      ],
      shouShuXZList: [
        {
          shouShuDM: '234112',
          shouShuMC: '甲状腺癌根治术',
          shouShuJB: '四级手术',
          ICD: 'ICD9'
        },
        {
          shouShuDM: '234113',
          shouShuMC: '甲状腺癌根治术',
          shouShuJB: '四级手术',
          ICD: 'ICD9'
        }
      ], //手术选择列表
      shouShuXZSelectionList: [],
      niShiShouShuList: [],
      shouShuXZCurrentPage: 1,
      shouShuXZPageSize: 7,
      shouShuQueryMC: '', //手术选择窗口查询手术名称
      benZhuanKe: true, //手术选择窗口本专科
      renYuanXM: '',
      qiYaZL: false,
      zhongDaSS: false
    }
  },
  computed: {
    ...mapState({
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID
    }),
    zhuDaoYSListFilter() {
      if (this.benZhuanKe) {
        return this.zhuDaoYSList.filter((item) => item.xianZhuanKeID == this.zhuanKeID)
      }
      return this.zhuDaoYSList
    },
    currentPageZhuDaoYSList() {
      return this.zhuDaoYSListFilter.slice(
        (this.currentPage - 1) * this.pageSize,
        (this.currentPage - 1) * this.pageSize + this.pageSize
      )
    },
    currentPageShouShuXZList() {
      return this.shouShuXZList.slice(
        (this.shouShuXZCurrentPage - 1) * this.shouShuXZPageSize,
        (this.shouShuXZCurrentPage - 1) * this.shouShuXZPageSize + this.shouShuXZPageSize
      )
    },
    shuZhongHUiZhenRowNum() {
      return function (length) {
        if (length % 3 == 0) {
          return length / 3
        } else {
          return Math.floor(length / 3) + 1
        }
      }
    },
    shuZhongHUiZhenColNum() {
      return function (rowNum, length) {
        console.log(rowNum, length)
        if (length % 3 == 0) {
          return 3
        } else {
          console.log(rowNum)
          if (rowNum < Math.floor(length / 3) + 1) {
            return 3
          } else {
            return length % 3
          }
        }
      }
    },
    shiShiShouShuMapper() {
      return function (shouShuList) {
        let shiShiShouShu = ''
        if (shouShuList != null) {
          shouShuList.map((item) => {
            shiShiShouShu += item.mingCheng
            if (item.shouShuBW != null) {
              shiShiShouShu += '(' + item.shouShuBW + ')'
            }
            shiShiShouShu += ' '
          })
        }
        return shiShiShouShu
      }
    },
    maZuiHZMapper: {
      get() {
        return this.shouShuTZD.maZuiHZ == '1' ? true : false
      },
      set(val) {
        this.shouShuTZD.maZuiHZ = val ? '1' : '0'
      }
    },
    xiaoZuTaiXuList() {
      return xiaoZuTaiXuList
    },
    bingQu() {
      return function (daiMa) {
        return this.inpatientInit.zhiXingBQs.find((item) => item.daiMa == daiMa).mingCheng
      }
    },
    formatDate() {
      return function (date, formatType) {
        if (!date) {
          return ''
        }
        console.log(date)
        return format(Date.parse(date), formatType)
      }
    },
    bingLiID() {
      return this.$route.params.id
    },
    zhuangTaiMC() {
      return function (zhuangTaiBZ) {
        switch (zhuangTaiBZ) {
          case '0':
            return '暂停'
          case '1':
            return '普通'
          case '2':
            return '锁定'
          case '3':
            return '医生已排班'
          case '4':
            return '护士已排班'
          case '5':
            return '已审核'
          case '6':
            return '删除'
          default:
            return '无'
        }
      }
    }
  },
  async mounted() {
    await this.init()
  },
  methods: {
    async init() {
      try {
        // 界面初始化
        const res = await inpatientInit({ bingLiID: this.bingLiID })
        if (res.hasError === 0) {
          this.inpatientInit = res.data
          this.zhuYuanID = this.inpatientInit.inPatientVo.zhuYuanID
        }

        const res1 = await initShouShuTZDList({
          bingLiID: this.bingLiID,
          leiBie: '0',
          zhuYuanID: this.zhuYuanID
        })
        if (res1.hasError === 0) {
          this.shouShuTZDList = res1.data.shouShuTZDList
        }
        const res2 = await initShouShuTZD({
          leiBie: '0',
          zhuYuanID: this.zhuYuanID,
          bingLiID: this.bingLiID,
          tongZhiDanID: 0
        })
        this.initTZD = res2.data
        const res3 = await getListByZhuanKeID({
          zhuanKeID: this.zhuanKeID
        })
        this.xinJiShuXXMList = res3.data
      } catch (error) {
        console.log(error)
      }
    },
    async handleAdd() {
      this.shouShuTZD = {}
      this.shuZhongHuiZhenList = []
      this.saveDisabled = false
    },
    async handleSave() {
      this.$message({
        message: '保存成功',
        type: 'success'
      })
      console.log(this.shouShuTZD)
    },
    async handleTZDInit(item) {
      const res = await initShouShuTZD({
        leiBie: '0',
        zhuYuanID: this.zhuYuanID,
        bingLiID: this.bingLiID,
        tongZhiDanID: item.shouShuXM[0].tongZhiDID
      })
      this.initTZD = res.data
      this.shouShuTZD = res.data.shouShuTZD
      this.saveDisabled = true
      console.log(this.initTZD)
    },
    async handleAddShuZhongHZ() {
      if (
        this.shuZhongHuiZhenList.length &&
        this.shuZhongHuiZhenList[this.shuZhongHuiZhenList.length - 1].huiZhenYSXM == ''
      ) {
        return
      }
      this.shuZhongHuiZhenList.push({ huiZhenYSXM: '' })
    },
    async selectYS(selectObj) {
      const res = await getShouShuRyk()
      this.zhuDaoYSList = res.data
      this.zhuDaoYSDialog = true
      this.selectInput = selectObj
      console.log(this.zhuanKeID)
    },
    selectShouShu() {
      this.niShiShouShuDialog = true
    },
    async selectXinJiShuXXM() {},
    async queryZhuDaoYS() {},
    handleSelectRY(item) {
      this.$set(this.shouShuTZD, this.selectInput, item.xingMing)
      this.zhuDaoYSDialog = false
    },
    handleSelectChange(selection) {
      this.shouShuXZSelectionList = selection
    },
    getRowKeys(row) {
      return row.shouShuDM
    },
    handleNiShiSSTagClose(event) {},
    handleShouShuBWClick(row, col, cell, event) {
      if (col.label == '手术部位') {
        this.shouShuBWDialog = true
        this.shouShuBWSelection = row
      }
    },
    handleSelectShouShuBW() {
      this.shouShuXZSelectionList.forEach((item) => {
        if (item.shouShuDM == this.shouShuBWSelection.shouShuDM) {
          this.$set(item, 'shouShuBW', this.shouShuBW)
        }
      })
      console.log(this.shouShuXZSelectionList)
      this.shouShuBWDialog = false
    },
    handleNiShiShouShuSubmit() {
      this.niShiShouShuList = this.shouShuXZSelectionList
      this.niShiShouShuDialog = false
    }
  }
}
</script>
<style>
.right-header-poperover {
  padding: 8px;
}
</style>
<style lang="scss" scoped>
.flex {
  display: flex;
  align-items: center;
}
.Dialog {
  ::v-deep .el-dialog__body {
    padding: 10px 20px;
    .renYuanXMInput {
      width: 43%;
      margin-right: 10px;
      .el-input__inner {
        padding: 0 6px;
        height: 24px;
        font-size: 13px;
      }
    }
    .renYuanList {
      margin-top: 20px;
      width: 100%;
      thead {
        background-color: #eaf0f9;
      }
      td {
        border: 1px solid #dcdfe6;
        border-collapse: collapse; /* 移除表格内边框间的间隙 */
        padding: 6px;
        font-size: 13px;
      }
      tbody {
        tr:nth-child(even) {
          background-color: #eaf0f9;
        }
        tr:nth-child(odd) {
          background-color: #f6f6f6;
        }
      }
    }
    .bottom-btn {
      margin-top: 30px;
      flex-direction: row-reverse;
      .el-button {
        margin-left: 10px;
      }
    }
  }
  .shouShuHeader {
    padding: 6px 10px;
    border: 1px solid #dcdfe6;
  }
  .shouShuTable {
    padding: 12px;
    border: 1px solid #dcdfe6;
    border-top: none;
    position: relative;
    .pagination {
      position: absolute;
      bottom: 10px;
      right: 10px;
    }
  }
  .shouShuXZList {
    height: 400px;
  }
  .yiXuanZeShouShuList {
    height: 242px;
  }
  .shouShuXZHeader {
    justify-content: space-between;
  }
  .yiXuanZeSSHeader {
    margin-top: 10px;
  }
}
.shouShuBWDialog {
  ::v-deep th {
    padding: 6px;
  }
  ::v-deep td {
    padding: 6px;
  }
  ::v-deep tr:nth-child(even) {
    background-color: #eff3fb !important;
  }
  ::v-deep tr:nth-child(odd) {
    background-color: #f6f6f6 !important;
  }
}
.text-center {
  text-align: center;
}
.title {
  font-weight: 600;
  border-left: 4px solid #356ac5;
  padding-left: 8px;
  text-align: left;
}

.popover {
  .popover-item {
    padding: 3px 0;
    cursor: pointer;
  }
  .popover-item:hover {
    background-color: #dedede;
  }
}
.surgical-notice-view {
  background-color: #fff;
  padding: 8px;
  height: 100%;
  .left-aside {
    width: 300px !important;
  }
  .right-aside {
    width: 220px !important;
  }
  .el-main {
    padding: 0 0 0 10px !important;
  }
  .left {
    background-color: #eff3fb;
    border-radius: 4px;
    padding: 8px;
    height: 100%;
    .left-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 10px;

      ::v-deep .el-button {
        padding: 6px 10px;
        background-color: #3b76ef;
      }
    }
    .surgical-list {
      height: 70%;
      border: 1px solid #dcdfe6;
      background-color: #eaf0f9;
      border-radius: 4px;
      padding: 10px;
      table {
        width: 100%;
        td {
          border: 1.5px solid #dcdfe6;
          border-collapse: collapse; /* 移除表格内边框间的间隙 */
          border-right: none;
        }
        tbody {
          border: 1px solid #3b76ef;
        }
        .table-title {
          color: #fff;
          background-color: rgba($color: #3b76ef, $alpha: 0.6);
          padding: 10px 13px;
          text-align: right;
        }
        .last-td {
          border-bottom: none;
          border-radius: 0 0 4px 4px;
        }
      }
      .table-header {
        text-align: center;
        background-color: #6787cc;
        color: #fff;
        padding: 12px 20px;
        border-radius: 4px 4px 0 0;
      }
    }
  }
  .right {
    background-color: #eff3fb;
    border-radius: 4px;
    padding: 8px;
    height: 100%;
    .right-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 5%;
      .button-group {
        display: flex;
        align-items: center;
        ::v-deep .el-popover__reference {
          background-color: #eff3fb;
          border: none;
        }
        .el-icon-more {
          transform: rotateZ(90deg);
          color: #356ac5;
          font-size: 18px;
        }
      }
    }
    .right-content {
      .niShiShouShuList {
        height: 100px;
        border: 1px solid #000;
        background-color: rgba(59, 118, 239, 0.1);
        border-radius: 4px;
        padding: 6px;
        .el-tag {
          border: 1px solid rgba($color: #155bd4, $alpha: 0.45) !important;
          margin: 2px;
        }
      }
      font-size: 12px;
      padding: 16px 8px;
      height: 95%;
      .el-main {
        .right-table {
          text-align: left;
          height: 100%;
          width: 100%;
          td {
            border-collapse: collapse;
            border: 1px solid #dcdfe6;
            padding: 6px;
          }
          .right-table-title {
            text-align: right;
            background-color: #eaf0f9;
            width: 9%;
            height: 100%;
          }
          .right-table-content {
            background-color: #ffffff;
            width: 20%;
            .el-checkbox {
              margin-right: 8px;
            }
          }
          .right-table-footer-tip {
            padding: 0 0 10px 0;
            background-color: #eaf0f9;
          }
          .select-input {
            ::v-deep .el-input__inner {
              background-color: #e4ecfb;
              height: 24px;
            }
          }
        }
      }
      .el-aside {
        margin: 0 0 0 10px !important;
        padding: 10px !important;
        border: 1px solid #dcdfe6;
        .right-aside-header {
          text-align: right;
          ::v-deep .el-button {
            background-color: #a66dd4;
            color: #fff;
          }
        }
        .right-aside-table {
          width: 100%;
          margin-top: 20px;
          td {
            border-collapse: collapse;
            border: 1px solid #dcdfe6;
            padding: 2px 6px;
          }
          thead {
            background-color: #eaf0f9;
          }
          tbody {
            td {
              background-color: #f6f6f6;
            }
          }
        }
      }
    }
  }
}
</style>
