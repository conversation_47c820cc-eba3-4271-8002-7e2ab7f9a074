<template>
  <div class="clinical-path-container">
    <!-- 上下布局-->
    <div class="clinical-path-header">
      <el-row class="clinical-path-btn">
        <el-button type="primary">保存</el-button>
        <el-button type="primary">加入支路径</el-button>
        <el-button type="primary">强制出径</el-button>
        <el-button class="clinical-path-btn-reevaluate">重新评估</el-button>
      </el-row>
    </div>
    <!-- 左右布局 -->
    <div class="clinical-path-layout">
      <!-- 左侧列表 -->
      <div class="left-panel">
        <div class="left-panel-header">
          <div class="title-log">路径选择</div>
          <div class="left-panel-container">
            <div class="lujing-lists">
              <div v-for="(item, key) in zhuLuJingBrrjjlVo" :key="key">
                <div v-if="key == 0" class="lujing-list-top">
                  主：{{ item.chuJingSJ }} {{ item.luJingMC }}
                </div>
                <div
                  v-else
                  :class="['lujing-list', key == LuJingIndex ? 'active' : '']"
                  @click="dbSelectLuJing(key)"
                >
                  {{ item.chuJingSJ_TXT }} {{ item.zhenLiaoJDMC }}
                </div>
              </div>
            </div>
            <div class="lujing-lists">
              <div v-for="(item, key) in zhiLuJingBrrjjlVoList" :key="key">
                <div v-if="key == 0" class="lujing-list-top">
                  次：{{ item.chuJingSJ }} {{ item.luJingMC }}
                </div>
                <div
                  v-else
                  :class="['lujing-list', key == LuJingIndex ? 'active' : '']"
                  @click="dbSelectLuJing(key)"
                >
                  {{ item.chuJingSJ_TXT }} {{ item.zhenLiaoJDMC }}{{ key - LuJingIndex }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧详情 -->
      <div class="right-panel">
        <div class="right-panel-header">
          <el-row :gutter="10">
            <el-col :span="12">
              <div class="title-log">待办事项列表</div>
              <el-table
                :data="daiBanShiXiangList"
                height="150px"
                highlight-current-row
                border
                stripe
                size="mini"
                @selection-change="handleSelectionChange"
              >
                <el-table-column type="selection" width="33"></el-table-column>
                <el-table-column prop="daiBanSXMC" label="事项名称"></el-table-column>
                <el-table-column prop="shiJiWCSJ" label="完成时间"></el-table-column>
              </el-table>
            </el-col>
            <el-col :span="12">
              <div class="title-log">已开医嘱列表</div>
              <el-table
                :data="yiKaiYiZhuList"
                height="150px"
                highlight-current-row
                border
                stripe
                size="mini"
              >
                <el-table-column prop="daiBanSXMC" label="事项名称"></el-table-column>
                <el-table-column prop="caoZuoZXM" label="操作人"></el-table-column>
                <el-table-column prop="xiuGaiSJ" label="修改时间"></el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </div>
        <div class="right-panel-container">
          <div class="title-log">待办事项详细内容</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getClinicalPathInit, getBingRenDbsxDetail, getBrDbsxYiZhu } from '@/api/clinical-path'
import { mapState } from 'vuex'
import { format } from 'date-fns'

export default {
  name: 'ClinicalPathway',
  data() {
    return {
      // 加载状态
      loading: false,
      //路径序号
      LuJingIndex: null,
      //路径选择数据
      zhuLuJingBrrjjlVo: [],
      ////支路径入径记录
      zhiLuJingBrrjjlVoList: [],
      //待办事项
      daiBanShiXiangList: [],
      //已开医嘱
      yiKaiYiZhuList: []
    }
  },
  computed: {
    bingLiID() {
      return this.$route.params.id
    },
    ...mapState({
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID
      // zhuanKeID: () => '42'
    })
  },
  created() {
    // 初始化数据
    this.fetchInitData()
  },
  methods: {
    // 获取初始化数据
    async fetchInitData() {
      this.loading = true
      try {
        // 获取初始化数据
        const initRes = await getClinicalPathInit({
          bingLiID: this.bingLiID
        })

        if (initRes.hasError === 0) {
          const data = initRes.data
          //处理数据（时间）
          if (data.zhuLuJingBrrjjlVo) {
            data.zhuLuJingBrrjjlVo.bingRenZlJdVoList.forEach((key) => {
              key.chuJingSJ_TXT = format(new Date(data.zhuLuJingBrrjjlVo.chuJingSJ), 'yyyy-MM-dd')
            })
            this.zhuLuJingBrrjjlVo = [
              {
                luJingMC: data.zhuLuJingBrrjjlVo.luJingMC,
                chuJingSJ: format(new Date(data.zhuLuJingBrrjjlVo.chuJingSJ), 'yyyy-MM-dd')
              },
              ...data.zhuLuJingBrrjjlVo.bingRenZlJdVoList
            ]
            this.zhiLuJingBrrjjlVoList = data.zhiLuJingBrrjjlVoList

            this.LuJingIndex = 1
            //查询_病人待办事项详情(日志)
            const sel = data.zhuLuJingBrrjjlVo.bingRenZlJdVoList[0] || {}
            this.loadInitData(sel)
          }
        }
      } catch (error) {
        console.error('初始化失败', error)
        this.$message.error('初始化失败')
      } finally {
        this.loading = false
      }
    },
    //
    async loadInitData(sel) {
      //查询_病人待办事项详情(日志)
      const danBanRes = await getBingRenDbsxDetail({
        bingLiID: this.bingLiID,
        luJingID: sel.luJingID,
        zhenLiaoJDID: sel.zhenLiaoJDID
      })
      if (danBanRes.hasError === 0) {
        const data = danBanRes.data
        if (data) {
          this.daiBanShiXiangList = [...data.huLiGZ, ...data.yiWanChengYZ, ...data.zhenLiaoGZ]
        }
      }
      //已开医嘱
      const yiKaiYiZhuRes = await getBrDbsxYiZhu({
        bingLiID: this.bingLiID,
        luJingID: sel.luJingID,
        zhenLiaoJDID: sel.zhenLiaoJDID
      })
      if (yiKaiYiZhuRes.hasError === 0) {
        this.yiKaiYiZhuList = yiKaiYiZhuRes.data
      }
    },
    //选择路径
    dbSelectLuJing(key) {
      const list = this.zhuLuJingBrrjjlVo[key]
      this.LuJingIndex = key
      this.loadInitData(list)
    },
    //
    async handleSelectionChange(obj) {}
  }
}
</script>

<style scoped lang="scss">
.clinical-path-container {
  height: 100%;
  width: 100%;
  padding: 10px;
  box-sizing: border-box;
  background-color: #fff;
  position: relative;
}

.clinical-path-header {
  height: 50px;
  display: flex;
  background-color: #eff3fb;
  border-radius: 4px;
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.1);
}

.clinical-path-btn {
  padding: 10px;
}

.clinical-path-btn-reevaluate {
  background-color: #a66dd4;
  color: #fff;
}

.clinical-path-layout {
  padding: 10px;
  display: flex;
  height: 100%;
  background-color: #eff3fb;
  margin-top: 10px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.left-panel {
  width: 300px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: #eff3fb;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.left-panel-header {
  padding: 10px;
}

.title-log {
  font-weight: 600;
  border-left: 4px solid #356ac5;
  padding-left: 8px;
  margin-bottom: 8px;
}

.lujing-list-top {
  border: 1px solid #dcdfe6;
  padding: 6px;
}

.lujing-list {
  border-left: 1px solid #dcdfe6;
  border-right: 1px solid #dcdfe6;
  border-bottom: 1px solid #dcdfe6;
  padding: 6px;
  cursor: pointer;
}

.lujing-lists .active {
  background-color: #356ac5;
  color: #fff;
}

.lujing-list:nth-child(even) {
  background-color: #fff;
}

.right-panel {
  flex: 1;
  margin-left: 10px;
  display: flex;
  flex-direction: column;
  background-color: #eff3fb;
  // padding: 10px;
  border-radius: 4px;
}

.right-panel-header {
  height: 210px;
  background-color: #eff3fb;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  padding: 10px;
  // box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.1);
}

.right-panel-container {
  height: 100%;
  width: 100%;
  padding: 10px;
  box-sizing: border-box;
  // background-color: #fff;
  position: relative;
  border: 1px solid #dcdfe6;
  margin-top: 10px;
}
</style>
